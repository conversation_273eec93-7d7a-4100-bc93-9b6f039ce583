# GameFlex LocalStack Setup

This document explains how to run the GameFlex backend using LocalStack for local development.

## Overview

The GameFlex backend has been converted to use LocalStack, which provides a fully functional local AWS cloud stack. This allows you to develop and test AWS services locally without needing real AWS resources.

## Prerequisites

- Docker and Docker Compose
- Node.js 18+
- AWS CLI
- LocalStack CLI (optional but recommended)
- jq (for JSON processing in scripts)

### Installing Prerequisites

```bash
# Install Docker (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install docker.io docker-compose

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Install jq
sudo apt-get install jq

# Install LocalStack CLI (optional)
pip install localstack
```

## Services Included

- **API Gateway**: REST API endpoints
- **Lambda**: Serverless functions for business logic
- **DynamoDB**: NoSQL database for data storage
- **IAM**: Identity and Access Management (basic simulation)

## Quick Start

1. **Start the services:**
   ```bash
   ./start.sh
   ```

2. **Stop the services:**
   ```bash
   ./stop.sh
   ```

3. **Stop and clean all data:**
   ```bash
   ./stop.sh --clean
   ```

## What the Start Script Does

1. Loads environment variables from `.env.localstack`
2. Checks Docker and required ports (4566, 8001)
3. Starts LocalStack containers with Docker Compose
4. Waits for LocalStack to be ready
5. Deploys infrastructure (DynamoDB tables, Lambda functions, API Gateway)
6. Seeds the database with test data
7. Displays service information

## Service URLs

- **LocalStack Gateway**: http://localhost:4566
- **DynamoDB Admin UI**: http://localhost:8001
- **Health Check**: http://localhost:4566/health

## API Endpoints

All API endpoints are available through LocalStack at `http://localhost:4566`:

- **Health**: `GET /health`
- **Authentication**: 
  - `POST /auth/signin`
  - `POST /auth/signup`
  - `POST /auth/refresh`
  - `GET /auth/validate`
- **Posts**:
  - `GET /posts`
  - `POST /posts`
  - `GET /posts/{id}`
  - `PUT /posts/{id}`
  - `DELETE /posts/{id}`
  - `POST /posts/{id}/like`
  - `DELETE /posts/{id}/like`
- **Users**:
  - `GET /users/profile`
  - `PUT /users/profile`
  - `GET /users/{id}`
  - `POST /users/{id}/follow`
  - `DELETE /users/{id}/follow`
- **Media**:
  - `POST /media/upload`
  - `GET /media/{id}`
  - `DELETE /media/{id}`

## Environment Configuration

The LocalStack configuration is stored in `.env.localstack`:

```bash
# LocalStack Configuration
LOCALSTACK_HOST=localhost
LOCALSTACK_ENDPOINT=http://localhost:4566
LOCALSTACK_AUTH_TOKEN=your-auth-token

# AWS Configuration for LocalStack
AWS_DEFAULT_REGION=us-west-2
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

# Project Configuration
ENVIRONMENT=development
PROJECT_NAME=gameflex
```

## Manual Operations

### Deploy Infrastructure Only
```bash
./scripts/deploy-localstack.sh
```

### Seed Data Only
```bash
./scripts/seed-localstack.sh
```

### View DynamoDB Tables
Visit http://localhost:8001 to use the DynamoDB Admin UI.

### Test API Endpoints
```bash
# Health check
curl http://localhost:4566/health

# Get posts (after seeding)
curl http://localhost:4566/posts
```

## Troubleshooting

### LocalStack Not Starting
- Check if Docker is running: `docker ps`
- Check if ports 4566 and 8001 are available
- View logs: `docker-compose logs localstack`

### Infrastructure Deployment Fails
- Ensure LocalStack is running and healthy
- Check LocalStack logs for errors
- Verify your auth token is correct

### Data Seeding Fails
- Ensure tables are created first
- Check if jq is installed
- Verify LocalStack endpoint is accessible

### Lambda Functions Not Working
- Check if functions are deployed: `aws lambda list-functions --endpoint-url http://localhost:4566`
- View function logs in LocalStack output

## Development Workflow

1. Start LocalStack: `./start.sh`
2. Make code changes to Lambda functions
3. Redeploy infrastructure: `./scripts/deploy-localstack.sh`
4. Test your changes
5. Stop when done: `./stop.sh`

## Data Persistence

LocalStack data is persisted in the `tmp/localstack` directory. To start fresh:

```bash
./stop.sh --clean
./start.sh
```

## Differences from AWS

- No real authentication (Cognito is simulated)
- S3 buckets are not used (will use Cloudflare in production)
- IAM policies are not enforced
- No real costs or limits
- Faster deployment and testing

## Next Steps

- Set up Cloudflare for file storage
- Add integration tests
- Configure CI/CD pipeline
- Add monitoring and logging
