const express = require('express');
const multer = require('multer');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8787;
const STORAGE_PATH = process.env.STORAGE_PATH || './storage';

// Ensure storage directories exist
const MEDIA_BUCKET = path.join(STORAGE_PATH, 'gameflex-media-dev');
const ASSETS_BUCKET = path.join(STORAGE_PATH, 'gameflex-assets-dev');

[STORAGE_PATH, MEDIA_BUCKET, ASSETS_BUCKET].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
});

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const bucket = req.path.includes('/media/') ? MEDIA_BUCKET : ASSETS_BUCKET;
    cb(null, bucket);
  },
  filename: (req, file, cb) => {
    const fileId = uuidv4();
    const extension = path.extname(file.originalname);
    const filename = `${fileId}${extension}`;
    req.fileId = fileId;
    req.filename = filename;
    cb(null, filename);
  }
});

const upload = multer({ 
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow common file types
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/webm', 'video/quicktime',
      'application/pdf', 'text/plain'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not allowed`), false);
    }
  }
});

// Helper functions
const getFileMetadata = (filePath) => {
  try {
    const stats = fs.statSync(filePath);
    return {
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime
    };
  } catch (error) {
    return null;
  }
};

const listBucketContents = (bucketPath) => {
  try {
    const files = fs.readdirSync(bucketPath);
    return files.map(filename => {
      const filePath = path.join(bucketPath, filename);
      const metadata = getFileMetadata(filePath);
      const fileId = path.parse(filename).name;
      
      return {
        key: filename,
        fileId: fileId,
        size: metadata?.size || 0,
        uploaded: metadata?.created || new Date(),
        modified: metadata?.modified || new Date()
      };
    });
  } catch (error) {
    return [];
  }
};

// Routes

// Health check
app.get('/health', (req, res) => {
  const mediaFiles = listBucketContents(MEDIA_BUCKET);
  const assetFiles = listBucketContents(ASSETS_BUCKET);
  
  res.json({
    status: 'healthy',
    service: 'gameflex-r2-simulator',
    timestamp: new Date().toISOString(),
    buckets: {
      media: {
        name: 'gameflex-media-dev',
        path: MEDIA_BUCKET,
        fileCount: mediaFiles.length,
        status: 'available'
      },
      assets: {
        name: 'gameflex-assets-dev', 
        path: ASSETS_BUCKET,
        fileCount: assetFiles.length,
        status: 'available'
      }
    },
    environment: process.env.NODE_ENV || 'development',
    storage: {
      basePath: STORAGE_PATH,
      totalFiles: mediaFiles.length + assetFiles.length
    }
  });
});

// List all buckets
app.get('/buckets', (req, res) => {
  const mediaFiles = listBucketContents(MEDIA_BUCKET);
  const assetFiles = listBucketContents(ASSETS_BUCKET);
  
  res.json({
    buckets: [
      {
        name: 'gameflex-media-dev',
        type: 'media',
        objectCount: mediaFiles.length,
        objects: mediaFiles.slice(0, 10) // Limit to first 10 for overview
      },
      {
        name: 'gameflex-assets-dev',
        type: 'assets', 
        objectCount: assetFiles.length,
        objects: assetFiles.slice(0, 10) // Limit to first 10 for overview
      }
    ],
    timestamp: new Date().toISOString()
  });
});

// Media upload
app.post('/media/upload', upload.single('file'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file provided' });
  }

  const fileInfo = {
    success: true,
    fileId: req.fileId,
    filename: req.filename,
    originalName: req.file.originalname,
    size: req.file.size,
    type: req.file.mimetype,
    bucket: 'gameflex-media-dev',
    url: `/media/${req.fileId}`,
    uploadedAt: new Date().toISOString()
  };

  console.log(`Media uploaded: ${req.filename} (${req.file.size} bytes)`);
  res.json(fileInfo);
});

// Assets upload
app.post('/assets/upload', upload.single('file'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file provided' });
  }

  const fileInfo = {
    success: true,
    fileId: req.fileId,
    filename: req.filename,
    originalName: req.file.originalname,
    size: req.file.size,
    type: req.file.mimetype,
    bucket: 'gameflex-assets-dev',
    url: `/assets/${req.fileId}`,
    uploadedAt: new Date().toISOString()
  };

  console.log(`Asset uploaded: ${req.filename} (${req.file.size} bytes)`);
  res.json(fileInfo);
});

// Media retrieval
app.get('/media/:fileId', (req, res) => {
  const { fileId } = req.params;
  const files = fs.readdirSync(MEDIA_BUCKET);
  const matchingFile = files.find(filename => filename.startsWith(fileId));
  
  if (!matchingFile) {
    return res.status(404).json({ error: 'File not found' });
  }

  const filePath = path.join(MEDIA_BUCKET, matchingFile);
  const metadata = getFileMetadata(filePath);
  
  if (!metadata) {
    return res.status(404).json({ error: 'File not found' });
  }

  // Set appropriate headers
  res.setHeader('Cache-Control', 'public, max-age=31536000');
  res.setHeader('Content-Length', metadata.size);
  
  // Send file
  res.sendFile(path.resolve(filePath));
});

// Assets retrieval
app.get('/assets/:fileId', (req, res) => {
  const { fileId } = req.params;
  const files = fs.readdirSync(ASSETS_BUCKET);
  const matchingFile = files.find(filename => filename.startsWith(fileId));
  
  if (!matchingFile) {
    return res.status(404).json({ error: 'File not found' });
  }

  const filePath = path.join(ASSETS_BUCKET, matchingFile);
  const metadata = getFileMetadata(filePath);
  
  if (!metadata) {
    return res.status(404).json({ error: 'File not found' });
  }

  // Set appropriate headers
  res.setHeader('Cache-Control', 'public, max-age=31536000');
  res.setHeader('Content-Length', metadata.size);
  
  // Send file
  res.sendFile(path.resolve(filePath));
});

// Media deletion
app.delete('/media/:fileId', (req, res) => {
  const { fileId } = req.params;
  const files = fs.readdirSync(MEDIA_BUCKET);
  const matchingFile = files.find(filename => filename.startsWith(fileId));
  
  if (!matchingFile) {
    return res.status(404).json({ error: 'File not found' });
  }

  const filePath = path.join(MEDIA_BUCKET, matchingFile);
  
  try {
    fs.unlinkSync(filePath);
    console.log(`Media deleted: ${matchingFile}`);
    res.json({ 
      success: true, 
      message: 'File deleted successfully',
      fileId: fileId,
      filename: matchingFile
    });
  } catch (error) {
    console.error('Delete error:', error);
    res.status(500).json({ error: 'Failed to delete file' });
  }
});

// Assets deletion
app.delete('/assets/:fileId', (req, res) => {
  const { fileId } = req.params;
  const files = fs.readdirSync(ASSETS_BUCKET);
  const matchingFile = files.find(filename => filename.startsWith(fileId));
  
  if (!matchingFile) {
    return res.status(404).json({ error: 'File not found' });
  }

  const filePath = path.join(ASSETS_BUCKET, matchingFile);
  
  try {
    fs.unlinkSync(filePath);
    console.log(`Asset deleted: ${matchingFile}`);
    res.json({ 
      success: true, 
      message: 'File deleted successfully',
      fileId: fileId,
      filename: matchingFile
    });
  } catch (error) {
    console.error('Delete error:', error);
    res.status(500).json({ error: 'Failed to delete file' });
  }
});

// Default route
app.get('/', (req, res) => {
  res.json({
    message: 'GameFlex R2 Development Simulator',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      buckets: '/buckets',
      media_upload: '/media/upload',
      media_get: '/media/{id}',
      media_delete: '/media/{id}',
      assets_upload: '/assets/upload',
      assets_get: '/assets/{id}',
      assets_delete: '/assets/{id}'
    },
    documentation: 'See README.md for usage instructions'
  });
});

// Error handling
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large (max 10MB)' });
    }
    return res.status(400).json({ error: error.message });
  }
  
  res.status(500).json({ 
    error: 'Internal server error',
    message: error.message 
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 GameFlex R2 Development Simulator running on port ${PORT}`);
  console.log(`📁 Storage path: ${STORAGE_PATH}`);
  console.log(`📷 Media bucket: ${MEDIA_BUCKET}`);
  console.log(`🗂️  Assets bucket: ${ASSETS_BUCKET}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Buckets info: http://localhost:${PORT}/buckets`);
});
