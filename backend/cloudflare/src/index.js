/**
 * GameFlex R2 Storage Development Worker
 * Provides local R2 storage simulation for development
 */

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // CORS headers for development
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      // Health check endpoint
      if (path === '/health') {
        return new Response(JSON.stringify({
          status: 'healthy',
          service: 'gameflex-r2-dev',
          timestamp: new Date().toISOString(),
          buckets: {
            media: env.GAMEFLEX_MEDIA_BUCKET ? 'available' : 'not_configured',
            assets: env.GAMEFLEX_ASSETS_BUCKET ? 'available' : 'not_configured'
          },
          environment: env.ENVIRONMENT || 'development'
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      // Media upload endpoint
      if (path.startsWith('/media/upload') && request.method === 'POST') {
        return await handleMediaUpload(request, env);
      }

      // Media retrieval endpoint
      if (path.startsWith('/media/') && request.method === 'GET') {
        return await handleMediaGet(request, env);
      }

      // Media deletion endpoint
      if (path.startsWith('/media/') && request.method === 'DELETE') {
        return await handleMediaDelete(request, env);
      }

      // Assets upload endpoint
      if (path.startsWith('/assets/upload') && request.method === 'POST') {
        return await handleAssetsUpload(request, env);
      }

      // Assets retrieval endpoint
      if (path.startsWith('/assets/') && request.method === 'GET') {
        return await handleAssetsGet(request, env);
      }

      // List buckets endpoint
      if (path === '/buckets' && request.method === 'GET') {
        return await handleListBuckets(request, env);
      }

      // Default response
      return new Response(JSON.stringify({
        message: 'GameFlex R2 Development Server',
        version: '1.0.0',
        endpoints: {
          health: '/health',
          media_upload: '/media/upload',
          media_get: '/media/{id}',
          media_delete: '/media/{id}',
          assets_upload: '/assets/upload',
          assets_get: '/assets/{id}',
          list_buckets: '/buckets'
        }
      }), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });

    } catch (error) {
      console.error('Error processing request:', error);
      return new Response(JSON.stringify({
        error: 'Internal server error',
        message: error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }
  }
};

// Media upload handler
async function handleMediaUpload(request, env) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    
    if (!file) {
      return new Response(JSON.stringify({ error: 'No file provided' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const fileId = crypto.randomUUID();
    const fileName = `${fileId}-${file.name}`;
    
    // Store in R2 bucket
    if (env.GAMEFLEX_MEDIA_BUCKET) {
      await env.GAMEFLEX_MEDIA_BUCKET.put(fileName, file.stream(), {
        httpMetadata: {
          contentType: file.type,
        },
        customMetadata: {
          originalName: file.name,
          uploadedAt: new Date().toISOString(),
        }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      fileId: fileId,
      fileName: fileName,
      originalName: file.name,
      size: file.size,
      type: file.type,
      url: `/media/${fileId}`
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Upload failed',
      message: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Media retrieval handler
async function handleMediaGet(request, env) {
  try {
    const url = new URL(request.url);
    const fileId = url.pathname.split('/media/')[1];
    
    if (!fileId) {
      return new Response('File ID required', { status: 400 });
    }

    if (env.GAMEFLEX_MEDIA_BUCKET) {
      // Try to find the file by ID prefix
      const objects = await env.GAMEFLEX_MEDIA_BUCKET.list({ prefix: fileId });
      
      if (objects.objects.length > 0) {
        const object = await env.GAMEFLEX_MEDIA_BUCKET.get(objects.objects[0].key);
        
        if (object) {
          return new Response(object.body, {
            headers: {
              'Content-Type': object.httpMetadata?.contentType || 'application/octet-stream',
              'Cache-Control': 'public, max-age=31536000',
            }
          });
        }
      }
    }

    return new Response('File not found', { status: 404 });

  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Retrieval failed',
      message: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Media deletion handler
async function handleMediaDelete(request, env) {
  try {
    const url = new URL(request.url);
    const fileId = url.pathname.split('/media/')[1];
    
    if (!fileId) {
      return new Response('File ID required', { status: 400 });
    }

    if (env.GAMEFLEX_MEDIA_BUCKET) {
      // Try to find and delete the file by ID prefix
      const objects = await env.GAMEFLEX_MEDIA_BUCKET.list({ prefix: fileId });
      
      if (objects.objects.length > 0) {
        await env.GAMEFLEX_MEDIA_BUCKET.delete(objects.objects[0].key);
        
        return new Response(JSON.stringify({
          success: true,
          message: 'File deleted successfully'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    return new Response('File not found', { status: 404 });

  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Deletion failed',
      message: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Assets upload handler (similar to media but for static assets)
async function handleAssetsUpload(request, env) {
  // Similar implementation to handleMediaUpload but for GAMEFLEX_ASSETS_BUCKET
  return handleMediaUpload(request, { GAMEFLEX_MEDIA_BUCKET: env.GAMEFLEX_ASSETS_BUCKET });
}

// Assets retrieval handler
async function handleAssetsGet(request, env) {
  // Similar implementation to handleMediaGet but for GAMEFLEX_ASSETS_BUCKET
  const modifiedRequest = new Request(request.url.replace('/assets/', '/media/'), request);
  return handleMediaGet(modifiedRequest, { GAMEFLEX_MEDIA_BUCKET: env.GAMEFLEX_ASSETS_BUCKET });
}

// List buckets handler
async function handleListBuckets(request, env) {
  try {
    const buckets = [];
    
    if (env.GAMEFLEX_MEDIA_BUCKET) {
      const mediaObjects = await env.GAMEFLEX_MEDIA_BUCKET.list({ limit: 10 });
      buckets.push({
        name: 'gameflex-media-dev',
        binding: 'GAMEFLEX_MEDIA_BUCKET',
        objectCount: mediaObjects.objects.length,
        objects: mediaObjects.objects.map(obj => ({
          key: obj.key,
          size: obj.size,
          uploaded: obj.uploaded
        }))
      });
    }

    if (env.GAMEFLEX_ASSETS_BUCKET) {
      const assetsObjects = await env.GAMEFLEX_ASSETS_BUCKET.list({ limit: 10 });
      buckets.push({
        name: 'gameflex-assets-dev',
        binding: 'GAMEFLEX_ASSETS_BUCKET',
        objectCount: assetsObjects.objects.length,
        objects: assetsObjects.objects.map(obj => ({
          key: obj.key,
          size: obj.size,
          uploaded: obj.uploaded
        }))
      });
    }

    return new Response(JSON.stringify({
      buckets: buckets,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Failed to list buckets',
      message: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
