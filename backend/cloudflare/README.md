# GameFlex Cloudflare R2 Development Simulator

This directory contains a local R2 storage simulator for GameFlex development.

## Overview

This setup provides:
- Local R2 storage simulation using Express.js
- File upload/download/delete operations
- CORS-enabled API endpoints
- Development-friendly logging and error handling
- File system-based storage (no external dependencies)

## Quick Start

The R2 development environment is automatically started with the main GameFlex backend:

```bash
# Start everything (including R2)
./start.sh

# Stop everything
./stop.sh
```

## Manual Operations

If you need to work with the R2 simulator separately:

```bash
# Enter the cloudflare directory
cd cloudflare

# Install dependencies (if not already done)
npm install

# Start local development server
npm run start

# Test the simulator
npm run test
```

## API Endpoints

When running, the R2 development simulator provides these endpoints:

- `GET /health` - Health check and bucket status
- `POST /media/upload` - Upload media files
- `GET /media/{id}` - Retrieve media files
- `DELETE /media/{id}` - Delete media files
- `POST /assets/upload` - Upload static assets
- `GET /assets/{id}` - Retrieve static assets
- `GET /buckets` - List all buckets and their contents

## Configuration

### Environment Variables

The following environment variables can be configured in `.dev.vars`:

- `API_BASE_URL` - Base URL for API calls
- `LOCALSTACK_ENDPOINT` - LocalStack endpoint for integration
- `ENVIRONMENT` - Current environment (development/production)
- `MAX_FILE_SIZE` - Maximum file upload size in bytes
- `ALLOWED_FILE_TYPES` - Comma-separated list of allowed MIME types

### Wrangler Configuration

The `wrangler.toml` file configures:
- R2 bucket bindings
- Development server settings
- Environment-specific variables
- Build configuration

## Integration with GameFlex Backend

The R2 development environment integrates with the main GameFlex backend:

1. **Media Storage**: Handles file uploads from the mobile app
2. **Asset Serving**: Serves static assets and user-generated content
3. **CORS Support**: Configured for cross-origin requests from the frontend
4. **LocalStack Integration**: Can communicate with LocalStack services

## Development Workflow

1. **File Upload**: POST multipart form data to `/media/upload`
2. **File Retrieval**: GET `/media/{fileId}` to download files
3. **File Management**: Use `/buckets` endpoint to list and manage files
4. **Health Monitoring**: Use `/health` endpoint to check service status

## Local vs Remote Development

- **Local Mode** (default): Files stored in local filesystem, no Cloudflare account needed
- **Remote Mode**: Files stored in actual Cloudflare R2, requires API credentials

To switch to remote mode, set your Cloudflare credentials in `.dev.vars` and use:
```bash
npm run dev:remote
```

## Troubleshooting

### Common Issues

1. **Port 8787 already in use**: Stop other Wrangler instances or change port in `wrangler.toml`
2. **Permission denied**: Ensure Docker has access to the `./tmp/cloudflare` directory
3. **Bucket not found**: Check R2 bucket configuration in `wrangler.toml`

### Logs

View detailed logs:
```bash
# View worker logs
npm run tail

# View Docker container logs
docker logs gameflex-cloudflare-r2
```

## Production Deployment

For production deployment:

1. Set up real Cloudflare R2 buckets
2. Configure production environment variables
3. Deploy using: `npm run deploy`

## Security Notes

- This is a development environment - do not use in production without proper security measures
- File uploads are not validated beyond basic type checking
- CORS is wide open for development convenience
- No authentication/authorization implemented
