# Development environment variables for Cloudflare Workers
# These are loaded automatically by wrangler dev

# API Configuration
API_BASE_URL=http://localhost:4566
LOCALSTACK_ENDPOINT=http://host.docker.internal:4566

# Environment
ENVIRONMENT=development

# Optional: Cloudflare credentials (if you want to test with real Cloudflare services)
# CLOUDFLARE_API_TOKEN=your_api_token_here
# CLOUDFLARE_ACCOUNT_ID=your_account_id_here

# CORS Configuration
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization

# File Upload Configuration
MAX_FILE_SIZE=********  # 10MB in bytes
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4,video/webm

# Storage Configuration
STORAGE_PREFIX=gameflex-dev
CACHE_TTL=3600
