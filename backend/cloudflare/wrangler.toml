name = "gameflex-r2-dev"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# R2 bucket bindings for local development
[[r2_buckets]]
binding = "GAMEFLEX_MEDIA_BUCKET"
bucket_name = "gameflex-media-dev"
preview_bucket_name = "gameflex-media-dev-preview"

[[r2_buckets]]
binding = "GAMEFLEX_ASSETS_BUCKET"
bucket_name = "gameflex-assets-dev"
preview_bucket_name = "gameflex-assets-dev-preview"

# Environment variables for local development
[vars]
ENVIRONMENT = "development"
API_BASE_URL = "http://localhost:4566"
LOCALSTACK_ENDPOINT = "http://host.docker.internal:4566"

# Development environment configuration
[env.development]
name = "gameflex-r2-dev"
vars = { ENVIRONMENT = "development" }

# Production environment configuration (for reference)
[env.production]
name = "gameflex-r2-prod"
vars = { ENVIRONMENT = "production" }

# Local development settings
[dev]
port = 8787
local_protocol = "http"
upstream_protocol = "https"
host = "0.0.0.0"

# Build configuration
[build]
command = ""
cwd = ""
watch_dir = "src"
