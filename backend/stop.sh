#!/bin/bash

# GameFlex LocalStack Backend Stop Script
# This script stops the LocalStack environment and Docker containers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

# Stop LocalStack containers
stop_localstack() {
    print_status "Stopping LocalStack containers..."

    # Stop Docker Compose services
    if command -v docker-compose &> /dev/null; then
        docker-compose down
    else
        docker compose down
    fi

    print_status "LocalStack containers stopped"
}

# Stop any remaining LocalStack processes
stop_localstack_processes() {
    print_status "Stopping any remaining LocalStack processes..."

    # Kill any remaining LocalStack processes
    pkill -f "localstack" 2>/dev/null || true

    print_status "LocalStack processes stopped"
}

# Clean up temporary files
cleanup_files() {
    print_status "Cleaning up temporary files..."

    # Remove any temporary files
    rm -f /tmp/gameflex_api_id

    # Clean up LocalStack data if requested
    if [ "$1" = "--clean" ]; then
        print_status "Cleaning LocalStack data..."
        rm -rf tmp/localstack
    fi

    print_status "Cleanup completed"
}

# Main execution
main() {
    print_header "Stopping GameFlex LocalStack Backend"
    echo

    stop_localstack
    stop_localstack_processes
    cleanup_files "$1"

    echo
    print_status "GameFlex LocalStack Backend stopped successfully!"

    if [ "$1" != "--clean" ]; then
        print_status "To clean all LocalStack data, run: ./stop.sh --clean"
    fi
}

# Run main function
main "$@"
