# Cloudflare R2 Development Environment Configuration
# This file contains environment variables for the Cloudflare R2 development setup

# Cloudflare API Configuration (optional for local development)
# Uncomment and set these if you want to use real Cloudflare services
# CLOUDFLARE_API_TOKEN=your_api_token_here
# CLOUDFLARE_ACCOUNT_ID=your_account_id_here

# Development Server Configuration
WRANGLER_LOG=debug
NODE_ENV=development

# R2 Storage Configuration
R2_DEV_PORT=8787
R2_DEV_HOST=0.0.0.0

# Integration with LocalStack
LOCALSTACK_ENDPOINT=http://host.docker.internal:4566
API_BASE_URL=http://localhost:4566

# File Upload Limits
MAX_FILE_SIZE=********  # 10MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4,video/webm,application/pdf

# CORS Configuration
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# Cache Configuration
CACHE_TTL=3600
STORAGE_PREFIX=gameflex-dev

# Bucket Names
MEDIA_BUCKET_NAME=gameflex-media-dev
ASSETS_BUCKET_NAME=gameflex-assets-dev

# Development Features
ENABLE_DEBUG_LOGS=true
ENABLE_CORS=true
ENABLE_FILE_VALIDATION=true
