{"Parameters": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "AWS_REGION": "us-west-2", "API_PORT": "3000", "DEBUG": "1", "AWS_ENDPOINT_URL": "http://localhost:4566", "LOCALSTACK_ENDPOINT": "http://localhost:4566", "DYNAMODB_ENDPOINT": "http://localhost:4566", "LAMBDA_ENDPOINT": "http://localhost:4566", "S3_ENDPOINT": "http://localhost:4566", "USER_POOL_ID": "us-west-2_localstack", "USER_POOL_CLIENT_ID": "localstack_client_id", "USERS_TABLE": "gameflex-development-Users", "POSTS_TABLE": "gameflex-development-Posts", "MEDIA_TABLE": "gameflex-development-Media", "USER_PROFILES_TABLE": "gameflex-development-UserProfiles", "COMMENTS_TABLE": "gameflex-development-Comments", "LIKES_TABLE": "gameflex-development-Likes", "FOLLOWS_TABLE": "gameflex-development-Follows", "MEDIA_BUCKET": "gameflex-media-development", "AVATARS_BUCKET": "gameflex-avatars-development", "TEMP_BUCKET": "gameflex-temp-development"}}