#!/bin/bash

# GameFlex LocalStack Backend Startup Script
# This script starts the LocalStack environment with Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

# Load environment variables from .env.localstack file
load_env_file() {
    if [ -f ".env.localstack" ]; then
        print_status "Loading environment variables from .env.localstack file..."
        # Export variables from .env.localstack file, ignoring comments and empty lines
        export $(grep -v '^#' .env.localstack | grep -v '^$' | xargs)
    else
        print_warning ".env.localstack file not found. Using default values."
    fi
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_status "Docker is running"
}

# Check if required ports are available
check_ports() {
    local ports=("4566" "8001")
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_warning "Port $port is already in use"
            read -p "Do you want to continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_error "Startup cancelled"
                exit 1
            fi
        fi
    done
    print_status "Port check completed"
}

# Check if Docker and Docker Compose are installed
check_docker_compose() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        print_status "Installation guide: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        print_status "Installation guide: https://docs.docker.com/compose/install/"
        exit 1
    fi

    print_status "Docker and Docker Compose are installed"
}

# Start LocalStack with Docker Compose
start_localstack() {
    print_status "Starting LocalStack with Docker Compose..."

    # Create tmp directory for LocalStack data
    mkdir -p tmp/localstack

    # Start LocalStack
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi

    print_status "LocalStack containers started"
}

# Wait for LocalStack to be ready
wait_for_localstack() {
    print_status "Waiting for LocalStack to be ready..."
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:4566/health" > /dev/null 2>&1; then
            print_status "LocalStack is ready!"
            return 0
        fi

        print_status "Attempt $attempt/$max_attempts: LocalStack not ready yet, waiting..."
        sleep 2
        ((attempt++))
    done

    print_error "LocalStack failed to start after $max_attempts attempts"
    return 1
}

# Deploy infrastructure to LocalStack
deploy_infrastructure() {
    print_status "Deploying infrastructure to LocalStack..."

    # Make scripts executable
    chmod +x scripts/*.sh

    # Run the LocalStack deployment script
    if [ -f "scripts/deploy-localstack.sh" ]; then
        ./scripts/deploy-localstack.sh
    else
        print_error "LocalStack deployment script not found!"
        return 1
    fi

    print_status "Infrastructure deployed successfully!"
}

# Seed data into LocalStack
seed_data() {
    print_status "Seeding data into LocalStack..."

    # Run the LocalStack seeding script
    if [ -f "scripts/seed-localstack.sh" ]; then
        ./scripts/seed-localstack.sh
    else
        print_warning "LocalStack seeding script not found. Skipping data seeding."
        return 0
    fi

    print_status "Data seeded successfully!"
}

# Display service information
display_info() {
    print_header "GameFlex LocalStack Backend is now running!"
    echo
    print_status "Service URLs:"
    echo "  🌐 LocalStack Gateway: http://localhost:4566"
    echo "  📊 DynamoDB Admin: http://localhost:8001"
    echo "  ☁️  Cloudflare R2 Dev: http://localhost:8787"
    echo
    print_status "API Endpoints (via LocalStack):"
    echo "  ❤️  Health: http://localhost:4566/health"
    echo "  🔐 Authentication: /auth/signin, /auth/signup, /auth/refresh"
    echo "  📝 Posts: /posts, /posts/{id}, /posts/{id}/like"
    echo "  📷 Media: /media/upload, /media/{id}"
    echo "  👤 Users: /users/profile, /users/{id}, /users/{id}/follow"
    echo
    print_status "Cloudflare R2 Endpoints:"
    echo "  ❤️  R2 Health: http://localhost:8787/health"
    echo "  📷 Media Upload: http://localhost:8787/media/upload"
    echo "  📷 Media Get: http://localhost:8787/media/{id}"
    echo "  🗂️  Assets Upload: http://localhost:8787/assets/upload"
    echo "  🗂️  Assets Get: http://localhost:8787/assets/{id}"
    echo "  📋 List Buckets: http://localhost:8787/buckets"
    echo
    print_status "LocalStack Resources:"
    echo "  📊 DynamoDB Tables: gameflex-development-*"
    echo "  🔧 Lambda Functions: gameflex-*-development"
    echo "  🌐 API Gateway: Available via LocalStack"
    echo
    print_status "Useful Commands:"
    echo "  🛑 Stop services: ./stop.sh"
    echo "  🔄 Restart: ./stop.sh && ./start.sh"
    echo "  📊 View DynamoDB: http://localhost:8001"
    echo "  🔍 LocalStack Health: curl http://localhost:4566/health"
    echo
    print_warning "This is a development environment using LocalStack!"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."

    # Stop Docker Compose services
    if command -v docker-compose &> /dev/null; then
        docker-compose down
    else
        docker compose down
    fi

    print_status "Cleanup completed"
}

# Trap SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header "GameFlex LocalStack Backend Startup"
    echo

    # Load environment variables from .env.localstack file
    load_env_file

    check_docker
    check_ports
    check_docker_compose

    start_localstack
    wait_for_localstack
    deploy_infrastructure
    seed_data
    display_info

    print_status "Startup completed successfully!"

    # Keep the script running to handle signals
    echo
    print_status "Press Ctrl+C to stop the services"
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"
