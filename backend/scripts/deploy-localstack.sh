#!/bin/bash

# GameFlex LocalStack Infrastructure Deployment Script
# This script deploys DynamoDB tables, Lambda functions, and API Gateway to LocalStack

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

# Configuration
LOCALSTACK_ENDPOINT="http://localhost:4566"
AWS_REGION="us-west-2"
PROJECT_NAME="gameflex"
ENVIRONMENT="development"

# Set AWS CLI to use LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=$AWS_REGION
export AWS_ENDPOINT_URL=$LOCALSTACK_ENDPOINT

# Wait for LocalStack to be ready
wait_for_localstack() {
    print_status "Waiting for LocalStack to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$LOCALSTACK_ENDPOINT/health" > /dev/null 2>&1; then
            print_status "LocalStack is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts: LocalStack not ready yet, waiting..."
        sleep 2
        ((attempt++))
    done
    
    print_error "LocalStack failed to start after $max_attempts attempts"
    return 1
}

# Create DynamoDB tables
create_dynamodb_tables() {
    print_header "Creating DynamoDB tables..."
    
    # Users table (simplified for LocalStack compatibility)
    print_status "Creating Users table..."
    aws dynamodb create-table \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Users" \
        --attribute-definitions \
            AttributeName=id,AttributeType=S \
        --key-schema \
            AttributeName=id,KeyType=HASH \
        --billing-mode PAY_PER_REQUEST \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "Users table may already exist"
    
    # Posts table
    print_status "Creating Posts table..."
    aws dynamodb create-table \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Posts" \
        --attribute-definitions \
            AttributeName=id,AttributeType=S \
        --key-schema \
            AttributeName=id,KeyType=HASH \
        --billing-mode PAY_PER_REQUEST \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "Posts table may already exist"
    
    # Media table
    print_status "Creating Media table..."
    aws dynamodb create-table \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Media" \
        --attribute-definitions \
            AttributeName=id,AttributeType=S \
        --key-schema \
            AttributeName=id,KeyType=HASH \
        --billing-mode PAY_PER_REQUEST \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "Media table may already exist"
    
    # UserProfiles table
    print_status "Creating UserProfiles table..."
    aws dynamodb create-table \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles" \
        --attribute-definitions \
            AttributeName=user_id,AttributeType=S \
        --key-schema \
            AttributeName=user_id,KeyType=HASH \
        --billing-mode PAY_PER_REQUEST \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "UserProfiles table may already exist"
    
    # Comments table
    print_status "Creating Comments table..."
    aws dynamodb create-table \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Comments" \
        --attribute-definitions \
            AttributeName=post_id,AttributeType=S \
            AttributeName=id,AttributeType=S \
        --key-schema \
            AttributeName=post_id,KeyType=HASH \
            AttributeName=id,KeyType=RANGE \
        --billing-mode PAY_PER_REQUEST \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "Comments table may already exist"
    
    # Likes table
    print_status "Creating Likes table..."
    aws dynamodb create-table \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Likes" \
        --attribute-definitions \
            AttributeName=post_id,AttributeType=S \
            AttributeName=user_id,AttributeType=S \
        --key-schema \
            AttributeName=post_id,KeyType=HASH \
            AttributeName=user_id,KeyType=RANGE \
        --billing-mode PAY_PER_REQUEST \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "Likes table may already exist"
    
    # Follows table
    print_status "Creating Follows table..."
    aws dynamodb create-table \
        --table-name "${PROJECT_NAME}-${ENVIRONMENT}-Follows" \
        --attribute-definitions \
            AttributeName=follower_id,AttributeType=S \
            AttributeName=following_id,AttributeType=S \
        --key-schema \
            AttributeName=follower_id,KeyType=HASH \
            AttributeName=following_id,KeyType=RANGE \
        --billing-mode PAY_PER_REQUEST \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "Follows table may already exist"
    
    print_status "DynamoDB tables created successfully!"
}

# Create Lambda functions
create_lambda_functions() {
    print_header "Creating Lambda functions..."
    
    # Create IAM role for Lambda (LocalStack doesn't enforce real permissions)
    print_status "Creating Lambda execution role..."
    aws iam create-role \
        --role-name lambda-execution-role \
        --assume-role-policy-document '{
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {
                        "Service": "lambda.amazonaws.com"
                    },
                    "Action": "sts:AssumeRole"
                }
            ]
        }' \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "Lambda role may already exist"
    
    # Get the script directory
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
    
    # Create deployment packages and deploy Lambda functions
    local functions=("auth" "posts" "media" "users" "health")
    
    for func in "${functions[@]}"; do
        print_status "Deploying $func function..."
        
        # Create deployment package
        local temp_dir=$(mktemp -d)
        cp -r "$PROJECT_ROOT/src/$func"/* "$temp_dir/"
        
        # Install dependencies if package.json exists
        if [ -f "$temp_dir/package.json" ]; then
            cd "$temp_dir"
            npm install --production --no-optional > /dev/null 2>&1
            cd - > /dev/null
        else
            # Copy node_modules from parent if it exists
            if [ -d "$PROJECT_ROOT/node_modules" ]; then
                cp -r "$PROJECT_ROOT/node_modules" "$temp_dir/"
            fi
        fi
        
        # Create zip file
        local zip_file="$temp_dir/$func.zip"
        cd "$temp_dir"
        zip -r "$zip_file" . > /dev/null 2>&1
        cd - > /dev/null
        
        # Deploy to LocalStack
        aws lambda create-function \
            --function-name "${PROJECT_NAME}-${func}-${ENVIRONMENT}" \
            --runtime nodejs20.x \
            --role arn:aws:iam::000000000000:role/lambda-execution-role \
            --handler index.handler \
            --zip-file "fileb://$zip_file" \
            --timeout 30 \
            --memory-size 256 \
            --environment Variables="{
                ENVIRONMENT=$ENVIRONMENT,
                PROJECT_NAME=$PROJECT_NAME,
                AWS_REGION=$AWS_REGION,
                LOCALSTACK_ENDPOINT=$LOCALSTACK_ENDPOINT,
                DYNAMODB_ENDPOINT=$LOCALSTACK_ENDPOINT,
                USERS_TABLE=${PROJECT_NAME}-${ENVIRONMENT}-Users,
                POSTS_TABLE=${PROJECT_NAME}-${ENVIRONMENT}-Posts,
                MEDIA_TABLE=${PROJECT_NAME}-${ENVIRONMENT}-Media,
                USER_PROFILES_TABLE=${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles,
                COMMENTS_TABLE=${PROJECT_NAME}-${ENVIRONMENT}-Comments,
                LIKES_TABLE=${PROJECT_NAME}-${ENVIRONMENT}-Likes,
                FOLLOWS_TABLE=${PROJECT_NAME}-${ENVIRONMENT}-Follows,
                USER_POOL_ID=us-west-2_localstack,
                USER_POOL_CLIENT_ID=localstack_client_id
            }" \
            --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1 || print_warning "$func function may already exist"
        
        # Clean up
        rm -rf "$temp_dir"
    done
    
    print_status "Lambda functions deployed successfully!"
}

# Create API Gateway
create_api_gateway() {
    print_header "Creating API Gateway..."

    # Create REST API
    print_status "Creating REST API..."
    local api_id=$(aws apigateway create-rest-api \
        --name "${PROJECT_NAME}-api-${ENVIRONMENT}" \
        --description "GameFlex API for LocalStack" \
        --endpoint-url $LOCALSTACK_ENDPOINT \
        --query 'id' --output text)

    if [ -z "$api_id" ]; then
        print_error "Failed to create API Gateway"
        return 1
    fi

    print_status "Created API with ID: $api_id"

    # Get root resource ID
    local root_resource_id=$(aws apigateway get-resources \
        --rest-api-id $api_id \
        --endpoint-url $LOCALSTACK_ENDPOINT \
        --query 'items[0].id' --output text)

    # Create resources and methods for each endpoint
    create_api_endpoints $api_id $root_resource_id

    # Deploy the API
    print_status "Deploying API..."
    aws apigateway create-deployment \
        --rest-api-id $api_id \
        --stage-name prod \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1

    print_status "API Gateway created successfully!"
    print_status "API URL: $LOCALSTACK_ENDPOINT/restapis/$api_id/prod/_user_request_"

    # Save API ID for later use
    echo $api_id > /tmp/gameflex_api_id
}

# Create API endpoints
create_api_endpoints() {
    local api_id=$1
    local root_resource_id=$2

    print_status "Creating API endpoints..."

    # Health endpoint
    create_api_resource_method $api_id $root_resource_id "health" "GET" "${PROJECT_NAME}-health-${ENVIRONMENT}"

    # Auth endpoints
    local auth_resource_id=$(create_api_resource $api_id $root_resource_id "auth")
    create_api_resource_method $api_id $auth_resource_id "signin" "POST" "${PROJECT_NAME}-auth-${ENVIRONMENT}"
    create_api_resource_method $api_id $auth_resource_id "signup" "POST" "${PROJECT_NAME}-auth-${ENVIRONMENT}"
    create_api_resource_method $api_id $auth_resource_id "refresh" "POST" "${PROJECT_NAME}-auth-${ENVIRONMENT}"
    create_api_resource_method $api_id $auth_resource_id "validate" "GET" "${PROJECT_NAME}-auth-${ENVIRONMENT}"

    # Posts endpoints
    local posts_resource_id=$(create_api_resource $api_id $root_resource_id "posts")
    create_api_resource_method $api_id $posts_resource_id "" "GET" "${PROJECT_NAME}-posts-${ENVIRONMENT}"
    create_api_resource_method $api_id $posts_resource_id "" "POST" "${PROJECT_NAME}-posts-${ENVIRONMENT}"

    # Users endpoints
    local users_resource_id=$(create_api_resource $api_id $root_resource_id "users")
    create_api_resource_method $api_id $users_resource_id "profile" "GET" "${PROJECT_NAME}-users-${ENVIRONMENT}"
    create_api_resource_method $api_id $users_resource_id "profile" "PUT" "${PROJECT_NAME}-users-${ENVIRONMENT}"

    # Media endpoints
    local media_resource_id=$(create_api_resource $api_id $root_resource_id "media")
    create_api_resource_method $api_id $media_resource_id "upload" "POST" "${PROJECT_NAME}-media-${ENVIRONMENT}"
}

# Helper function to create API resource
create_api_resource() {
    local api_id=$1
    local parent_id=$2
    local path_part=$3

    aws apigateway create-resource \
        --rest-api-id $api_id \
        --parent-id $parent_id \
        --path-part $path_part \
        --endpoint-url $LOCALSTACK_ENDPOINT \
        --query 'id' --output text
}

# Helper function to create API method and integration
create_api_resource_method() {
    local api_id=$1
    local resource_id=$2
    local path_part=$3
    local method=$4
    local function_name=$5

    # Create resource if path_part is provided
    if [ -n "$path_part" ]; then
        resource_id=$(create_api_resource $api_id $resource_id $path_part)
    fi

    # Create method
    aws apigateway put-method \
        --rest-api-id $api_id \
        --resource-id $resource_id \
        --http-method $method \
        --authorization-type NONE \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1

    # Create integration
    aws apigateway put-integration \
        --rest-api-id $api_id \
        --resource-id $resource_id \
        --http-method $method \
        --type AWS_PROXY \
        --integration-http-method POST \
        --uri "arn:aws:apigateway:$AWS_REGION:lambda:path/2015-03-31/functions/arn:aws:lambda:$AWS_REGION:000000000000:function:$function_name/invocations" \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1
}

# Main execution
main() {
    print_header "Starting LocalStack infrastructure deployment..."

    wait_for_localstack
    create_dynamodb_tables
    create_lambda_functions
    create_api_gateway

    print_status "✅ LocalStack infrastructure deployment completed!"
    print_status "🌐 LocalStack endpoint: $LOCALSTACK_ENDPOINT"
    print_status "📊 DynamoDB Admin: http://localhost:8001"
    print_status "🔍 Health check: $LOCALSTACK_ENDPOINT/health"

    # Show API URL if available
    if [ -f /tmp/gameflex_api_id ]; then
        local api_id=$(cat /tmp/gameflex_api_id)
        print_status "🚀 API Gateway: $LOCALSTACK_ENDPOINT/restapis/$api_id/prod/_user_request_"
    fi
}

# Run main function
main "$@"
