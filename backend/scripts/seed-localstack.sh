#!/bin/bash

# GameFlex LocalStack Data Seeding Script
# This script seeds LocalStack DynamoDB with test data for development

set -e

echo "[SEED] Starting GameFlex LocalStack data seeding..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SEED]${NC} $1"
}

# Environment variables
ENVIRONMENT=${ENVIRONMENT:-development}
PROJECT_NAME=${PROJECT_NAME:-gameflex}
AWS_REGION=${AWS_REGION:-us-west-2}
LOCALSTACK_ENDPOINT=${LOCALSTACK_ENDPOINT:-http://localhost:4566}

# Set AWS CLI to use LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=$AWS_REGION
export AWS_ENDPOINT_URL=$LOCALSTACK_ENDPOINT

# Table names (LocalStack format)
USERS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Users"
USER_PROFILES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles"
POSTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Posts"
MEDIA_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Media"
COMMENTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Comments"
LIKES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Likes"
FOLLOWS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Follows"

# Helper functions
item_exists() {
    local table_name=$1
    local key_condition=$2
    
    aws dynamodb get-item \
        --table-name "$table_name" \
        --key "$key_condition" \
        --query 'Item' \
        --output text \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1
}

put_item() {
    local table_name=$1
    local item_json=$2
    local item_description=$3

    # Check if item already exists for certain tables
    local should_check_exists=false
    local key_condition=""
    
    case "$table_name" in
        *Users)
            should_check_exists=true
            local user_id=$(echo "$item_json" | jq -r '.id.S // empty')
            if [ -n "$user_id" ]; then
                key_condition='{"id":{"S":"'$user_id'"}}'
            fi
            ;;
        *UserProfiles)
            should_check_exists=true
            local user_id=$(echo "$item_json" | jq -r '.user_id.S // empty')
            if [ -n "$user_id" ]; then
                key_condition='{"user_id":{"S":"'$user_id'"}}'
            fi
            ;;
        *Posts)
            should_check_exists=true
            local post_id=$(echo "$item_json" | jq -r '.id.S // empty')
            if [ -n "$post_id" ]; then
                key_condition='{"id":{"S":"'$post_id'"}}'
            fi
            ;;
        *Media)
            should_check_exists=true
            local media_id=$(echo "$item_json" | jq -r '.id.S // empty')
            if [ -n "$media_id" ]; then
                key_condition='{"id":{"S":"'$media_id'"}}'
            fi
            ;;
    esac

    # Check if item exists and skip if it does
    if [ "$should_check_exists" = true ] && [ -n "$key_condition" ]; then
        if item_exists "$table_name" "$key_condition"; then
            print_status "$item_description already exists, skipping"
            return 0
        fi
    fi

    print_status "Adding $item_description to $table_name..."
    
    local temp_file=$(mktemp)
    echo "$item_json" > "$temp_file"
    
    if timeout 30 aws dynamodb put-item \
        --table-name "$table_name" \
        --item file://"$temp_file" \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1; then
        print_status "✅ Successfully added $item_description"
    else
        print_error "❌ Failed to add $item_description"
        # Show the actual error for debugging
        echo "Error details:" >&2
        timeout 30 aws dynamodb put-item \
            --table-name "$table_name" \
            --item file://"$temp_file" \
            --endpoint-url $LOCALSTACK_ENDPOINT 2>&1 | head -3 >&2
        rm -f "$temp_file"
        return 1
    fi
    
    rm -f "$temp_file"
    return 0
}

# Test LocalStack connection
test_localstack_connection() {
    print_status "Testing LocalStack connection..."
    if curl -s "$LOCALSTACK_ENDPOINT/health" > /dev/null 2>&1; then
        print_status "LocalStack connection successful"
        print_status "Endpoint: $LOCALSTACK_ENDPOINT"
        return 0
    else
        print_error "Failed to connect to LocalStack at $LOCALSTACK_ENDPOINT"
        print_error "Make sure LocalStack is running: docker-compose up -d"
        return 1
    fi
}

# Check if required tables exist
check_tables() {
    print_status "Checking if required tables exist..."
    
    local tables=("$USERS_TABLE" "$USER_PROFILES_TABLE" "$POSTS_TABLE" "$MEDIA_TABLE" "$COMMENTS_TABLE" "$LIKES_TABLE" "$FOLLOWS_TABLE")
    local missing_tables=()
    
    for table in "${tables[@]}"; do
        if aws dynamodb describe-table --table-name "$table" --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1; then
            print_status "✅ Table $table exists"
        else
            print_warning "❌ Table $table does not exist"
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -gt 0 ]; then
        print_error "Missing tables: ${missing_tables[*]}"
        print_error "Run the deployment script first: ./scripts/deploy-localstack.sh"
        return 1
    fi
    
    return 0
}

# Wait for LocalStack to be ready
wait_for_localstack() {
    print_status "Waiting for LocalStack to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$LOCALSTACK_ENDPOINT/health" > /dev/null 2>&1; then
            print_status "LocalStack is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts: LocalStack not ready yet, waiting..."
        sleep 2
        ((attempt++))
    done
    
    print_error "LocalStack failed to start after $max_attempts attempts"
    return 1
}

# Main execution starts here
main() {
    print_header "Starting GameFlex LocalStack data seeding..."
    echo
    
    # Test LocalStack connection
    if ! test_localstack_connection; then
        exit 1
    fi
    echo
    
    # Check if tables exist
    if ! check_tables; then
        exit 1
    fi
    echo
    
    # Debug: Show configuration
    print_status "Using configuration:"
    print_status "  Environment: $ENVIRONMENT"
    print_status "  Project: $PROJECT_NAME"
    print_status "  Region: $AWS_REGION"
    print_status "  LocalStack Endpoint: $LOCALSTACK_ENDPOINT"
    print_status "  Users Table: $USERS_TABLE"
    print_status "  Posts Table: $POSTS_TABLE"
    print_status "  Media Table: $MEDIA_TABLE"
    echo
    
    # Start seeding
    seed_users
    echo
    seed_user_profiles
    echo
    seed_media
    echo
    seed_posts
    echo
    seed_comments
    echo
    seed_likes
    echo
    seed_follows
    echo
    
    print_status "🎉 LocalStack seeding finished successfully!"
    print_summary
}

# Seed Users table
seed_users() {
    print_header "Seeding Users table..."

    put_item "$USERS_TABLE" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "dev-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "developer"},
        "display_name": {"S": "GameFlex Developer"},
        "bio": {"S": "Development account for testing GameFlex features"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "developer user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "admin-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "admin"},
        "display_name": {"S": "GameFlex Admin"},
        "bio": {"S": "Administrator account with full access"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "admin user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0003"},
        "cognito_user_id": {"S": "john-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "johndoe"},
        "display_name": {"S": "John Doe"},
        "bio": {"S": "Gaming enthusiast and content creator."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "john doe user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-************"},
        "cognito_user_id": {"S": "jane-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "janesmith"},
        "display_name": {"S": "Jane Smith"},
        "bio": {"S": "Professional gamer and streamer."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "jane smith user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0005"},
        "cognito_user_id": {"S": "mike-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "mikewilson"},
        "display_name": {"S": "Mike Wilson"},
        "bio": {"S": "Casual gamer who loves sharing gaming moments."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "mike wilson user"

    print_status "Users seeded successfully!"
}

# Seed UserProfiles table
seed_user_profiles() {
    print_header "Seeding UserProfiles table..."

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Dev"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "dev profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Admin"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "admin profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "first_name": {"S": "John"},
        "last_name": {"S": "Doe"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "john profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-************"},
        "first_name": {"S": "Jane"},
        "last_name": {"S": "Smith"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "jane profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "first_name": {"S": "Mike"},
        "last_name": {"S": "Wilson"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Chicago"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "mike profile"

    print_status "User profiles seeded successfully!"
}

# Seed Media table
seed_media() {
    print_header "Seeding Media table..."

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "30000000-0000-0000-0000-********0001"},
        "fileName": {"S": "cod_screenshot.jpg"},
        "fileType": {"S": "image/jpeg"},
        "fileSize": {"N": "245760"},
        "mediaType": {"S": "image"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "s3Key": {"S": "user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
        "bucketName": {"S": "gameflex-media-development"},
        "url": {"S": "http://localhost:4566/gameflex-media-development/user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
        "status": {"S": "uploaded"},
        "created_at": {"S": "2024-12-28T14:00:00Z"},
        "updated_at": {"S": "2024-12-28T14:00:00Z"}
    }' "cod screenshot media"

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "30000000-0000-0000-0000-********0002"},
        "fileName": {"S": "diablo_screenshot.webp"},
        "fileType": {"S": "image/webp"},
        "fileSize": {"N": "189440"},
        "mediaType": {"S": "image"},
        "userId": {"S": "********-0000-0000-0000-************"},
        "s3Key": {"S": "user/********-0000-0000-0000-************/diablo_screenshot.webp"},
        "bucketName": {"S": "gameflex-media-development"},
        "url": {"S": "http://localhost:4566/gameflex-media-development/user/********-0000-0000-0000-************/diablo_screenshot.webp"},
        "status": {"S": "uploaded"},
        "created_at": {"S": "2024-12-27T16:00:00Z"},
        "updated_at": {"S": "2024-12-27T16:00:00Z"}
    }' "diablo screenshot media"

    print_status "Media seeded successfully!"
}

# Seed Posts table
seed_posts() {
    print_header "Seeding Posts table..."

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0001"},
        "author_id": {"S": "********-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥"},
        "media_id": {"S": "30000000-0000-0000-0000-********0001"},
        "likes": {"N": "12"},
        "comments": {"N": "4"},
        "created_at": {"S": "2024-12-28T14:30:00Z"},
        "updated_at": {"S": "2024-12-28T14:30:00Z"}
    }' "john cod post"

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0002"},
        "author_id": {"S": "********-0000-0000-0000-************"},
        "userId": {"S": "********-0000-0000-0000-************"},
        "content": {"S": "Finally defeated this boss in Diablo! The loot was totally worth the grind 💀⚔️"},
        "media_id": {"S": "30000000-0000-0000-0000-********0002"},
        "likes": {"N": "18"},
        "comments": {"N": "6"},
        "created_at": {"S": "2024-12-27T16:45:00Z"},
        "updated_at": {"S": "2024-12-27T16:45:00Z"}
    }' "jane diablo post"

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0003"},
        "author_id": {"S": "********-0000-0000-0000-********0005"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "Anyone else excited for the new gaming releases this month? What are you most looking forward to? 🎮"},
        "likes": {"N": "5"},
        "comments": {"N": "2"},
        "created_at": {"S": "2024-12-26T10:15:00Z"},
        "updated_at": {"S": "2024-12-26T10:15:00Z"}
    }' "mike text post"

    print_status "Posts seeded successfully!"
}

# Seed Comments table
seed_comments() {
    print_header "Seeding Comments table..."

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0001"},
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-************"},
        "content": {"S": "Nice clutch! What loadout were you using?"},
        "like_count": {"N": "3"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T15:15:00Z"},
        "updated_at": {"S": "2024-12-28T15:15:00Z"}
    }' "comment 1"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0002"},
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "That was insane! 🔥"},
        "like_count": {"N": "1"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T16:20:00Z"},
        "updated_at": {"S": "2024-12-28T16:20:00Z"}
    }' "comment 2"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0003"},
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "Congrats! What difficulty level?"},
        "like_count": {"N": "2"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T17:30:00Z"},
        "updated_at": {"S": "2024-12-27T17:30:00Z"}
    }' "comment 3"

    print_status "Comments seeded successfully!"
}

# Seed Likes table
seed_likes() {
    print_header "Seeding Likes table..."

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-************"},
        "created_at": {"S": "2024-12-28T14:35:00Z"}
    }' "like 1"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "created_at": {"S": "2024-12-28T14:40:00Z"}
    }' "like 2"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "created_at": {"S": "2024-12-27T16:50:00Z"}
    }' "like 3"

    print_status "Likes seeded successfully!"
}

# Seed Follows table
seed_follows() {
    print_header "Seeding Follows table..."

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "following_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-01-01T00:00:00Z"}
    }' "dev follows admin"

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "********-0000-0000-0000-********0003"},
        "following_id": {"S": "********-0000-0000-0000-************"},
        "created_at": {"S": "2024-01-02T00:00:00Z"}
    }' "john follows jane"

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "********-0000-0000-0000-************"},
        "following_id": {"S": "********-0000-0000-0000-********0005"},
        "created_at": {"S": "2024-01-03T00:00:00Z"}
    }' "jane follows mike"

    print_status "Follows seeded successfully!"
}

# Print summary
print_summary() {
    print_status "Summary:"
    print_status "  📊 Seeded 5 users with profiles"
    print_status "  📝 Seeded 3 posts (2 with media, 1 text-only)"
    print_status "  💬 Seeded 3 comments"
    print_status "  ❤️  Seeded 3 likes"
    print_status "  👥 Seeded 3 follow relationships"
    print_status "  📸 Seeded 2 media items"
    print_status ""
    print_status "Your GameFlex LocalStack backend is now ready for testing!"
    print_status "LocalStack endpoint: $LOCALSTACK_ENDPOINT"
    print_status "DynamoDB Admin: http://localhost:8001"
    print_status ""
    print_status "Test the health endpoint:"
    print_status "  curl $LOCALSTACK_ENDPOINT/health"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    print_error "Ubuntu/Debian: sudo apt-get install jq"
    print_error "macOS: brew install jq"
    exit 1
fi

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
