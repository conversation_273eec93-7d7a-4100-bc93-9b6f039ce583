#!/bin/bash

# GameFlex LocalStack Data Seeding Script
# This script seeds LocalStack DynamoDB with test data for development

set -e

echo "[SEED] Starting GameFlex LocalStack data seeding..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SEED]${NC} $1"
}

# Environment variables
ENVIRONMENT=${ENVIRONMENT:-development}
PROJECT_NAME=${PROJECT_NAME:-gameflex}
AWS_REGION=${AWS_REGION:-us-west-2}
LOCALSTACK_ENDPOINT=${LOCALSTACK_ENDPOINT:-http://localhost:4566}
R2_ENDPOINT=${R2_ENDPOINT:-http://localhost:8787}

# Set AWS CLI to use LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=$AWS_REGION
export AWS_ENDPOINT_URL=$LOCALSTACK_ENDPOINT

# Table names (LocalStack format)
USERS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Users"
USER_PROFILES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles"
POSTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Posts"
MEDIA_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Media"
COMMENTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Comments"
LIKES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Likes"
FOLLOWS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Follows"

# Helper functions

put_item() {
    local table_name=$1
    local item_json=$2
    local item_description=$3

    print_status "Adding $item_description to $table_name..."

    local temp_file=$(mktemp)
    echo "$item_json" > "$temp_file"

    if timeout 30 aws dynamodb put-item \
        --table-name "$table_name" \
        --item file://"$temp_file" \
        --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1; then
        print_status "✅ Successfully added $item_description"
    else
        # Try to get more specific error information
        local error_output=$(timeout 30 aws dynamodb put-item \
            --table-name "$table_name" \
            --item file://"$temp_file" \
            --endpoint-url $LOCALSTACK_ENDPOINT 2>&1)

        # Check if it's a conditional check failed error (item already exists)
        if echo "$error_output" | grep -q "ConditionalCheckFailedException\|already exists\|duplicate"; then
            print_status "✅ $item_description already exists, skipping"
        else
            print_error "❌ Failed to add $item_description"
            echo "Error details:" >&2
            echo "$error_output" | head -3 >&2
            rm -f "$temp_file"
            return 1
        fi
    fi

    rm -f "$temp_file"
    return 0
}

# Test LocalStack connection
test_localstack_connection() {
    print_status "Testing LocalStack connection..."
    if curl -s "$LOCALSTACK_ENDPOINT/health" > /dev/null 2>&1; then
        print_status "LocalStack connection successful"
        print_status "Endpoint: $LOCALSTACK_ENDPOINT"
        return 0
    else
        print_error "Failed to connect to LocalStack at $LOCALSTACK_ENDPOINT"
        print_error "Make sure LocalStack is running: docker-compose up -d"
        return 1
    fi
}

# Check if required tables exist
check_tables() {
    print_status "Checking if required tables exist..."
    
    local tables=("$USERS_TABLE" "$USER_PROFILES_TABLE" "$POSTS_TABLE" "$MEDIA_TABLE" "$COMMENTS_TABLE" "$LIKES_TABLE" "$FOLLOWS_TABLE")
    local missing_tables=()
    
    for table in "${tables[@]}"; do
        if aws dynamodb describe-table --table-name "$table" --endpoint-url $LOCALSTACK_ENDPOINT > /dev/null 2>&1; then
            print_status "✅ Table $table exists"
        else
            print_warning "❌ Table $table does not exist"
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -gt 0 ]; then
        print_error "Missing tables: ${missing_tables[*]}"
        print_error "Run the deployment script first: ./scripts/deploy-localstack.sh"
        return 1
    fi
    
    return 0
}

# Wait for LocalStack to be ready
wait_for_localstack() {
    print_status "Waiting for LocalStack to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$LOCALSTACK_ENDPOINT/health" > /dev/null 2>&1; then
            print_status "LocalStack is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts: LocalStack not ready yet, waiting..."
        sleep 2
        ((attempt++))
    done
    
    print_error "LocalStack failed to start after $max_attempts attempts"
    return 1
}

# Main execution starts here
main() {
    print_header "Starting GameFlex LocalStack data seeding..."
    echo
    
    # Test LocalStack connection
    if ! test_localstack_connection; then
        exit 1
    fi
    echo
    
    # Check if tables exist
    if ! check_tables; then
        exit 1
    fi
    echo
    
    # Debug: Show configuration
    print_status "Using configuration:"
    print_status "  Environment: $ENVIRONMENT"
    print_status "  Project: $PROJECT_NAME"
    print_status "  Region: $AWS_REGION"
    print_status "  LocalStack Endpoint: $LOCALSTACK_ENDPOINT"
    print_status "  Users Table: $USERS_TABLE"
    print_status "  Posts Table: $POSTS_TABLE"
    print_status "  Media Table: $MEDIA_TABLE"
    echo
    
    # Start seeding
    seed_users
    echo
    seed_user_profiles
    echo
    upload_media_to_r2
    echo
    seed_media
    echo
    seed_posts
    echo
    seed_comments
    echo
    seed_likes
    echo
    seed_follows
    echo
    
    print_status "🎉 LocalStack seeding finished successfully!"
    print_summary
}

# Seed Users table
seed_users() {
    print_header "Seeding Users table..."

    put_item "$USERS_TABLE" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "dev-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "developer"},
        "display_name": {"S": "GameFlex Developer"},
        "bio": {"S": "Development account for testing GameFlex features"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "developer user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "550e8400-e29b-41d4-a716-************"},
        "cognito_user_id": {"S": "admin-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "admin"},
        "display_name": {"S": "GameFlex Admin"},
        "bio": {"S": "Administrator account with full access"},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "admin user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0003"},
        "cognito_user_id": {"S": "john-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "johndoe"},
        "display_name": {"S": "John Doe"},
        "bio": {"S": "Gaming enthusiast and content creator."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "john doe user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0004"},
        "cognito_user_id": {"S": "jane-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "janesmith"},
        "display_name": {"S": "Jane Smith"},
        "bio": {"S": "Professional gamer and streamer."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "jane smith user"

    put_item "$USERS_TABLE" '{
        "id": {"S": "********-0000-0000-0000-********0005"},
        "cognito_user_id": {"S": "mike-cognito-placeholder"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "mikewilson"},
        "display_name": {"S": "Mike Wilson"},
        "bio": {"S": "Casual gamer who loves sharing gaming moments."},
        "is_active": {"BOOL": true},
        "is_verified": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "mike wilson user"

    print_status "Users seeded successfully!"
}

# Seed UserProfiles table
seed_user_profiles() {
    print_header "Seeding UserProfiles table..."

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Dev"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "dev profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "first_name": {"S": "Admin"},
        "last_name": {"S": "User"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "admin profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "first_name": {"S": "John"},
        "last_name": {"S": "Doe"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/New_York"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "john profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-********0004"},
        "first_name": {"S": "Jane"},
        "last_name": {"S": "Smith"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Los_Angeles"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "jane profile"

    put_item "$USER_PROFILES_TABLE" '{
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "first_name": {"S": "Mike"},
        "last_name": {"S": "Wilson"},
        "country": {"S": "United States"},
        "timezone": {"S": "America/Chicago"},
        "language": {"S": "en"},
        "created_at": {"S": "2024-01-01T00:00:00Z"},
        "updated_at": {"S": "2024-01-01T00:00:00Z"}
    }' "mike profile"

    print_status "User profiles seeded successfully!"
}

# Upload media files to R2 simulator
upload_media_to_r2() {
    print_header "Uploading media files to R2 simulator..."

    # Check if R2 simulator is running
    if ! curl -s "$R2_ENDPOINT/health" > /dev/null 2>&1; then
        print_warning "R2 simulator not available at $R2_ENDPOINT, skipping media upload"
        return 0
    fi

    # Get the script directory to find assets
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    ASSETS_DIR="$(dirname "$SCRIPT_DIR")/assets/media"

    print_status "Looking for media files in: $ASSETS_DIR"



    # Set default file IDs
    COD_FILE_ID="30000000-0000-0000-0000-********0001"
    DIABLO_FILE_ID="30000000-0000-0000-0000-********0002"

    # Try to upload COD screenshot
    print_status "Uploading COD screenshot to R2..."
    if [ -f "$ASSETS_DIR/cod_screenshot.jpg" ]; then
        local cod_response=$(curl -s -X POST -F "file=@$ASSETS_DIR/cod_screenshot.jpg" "$R2_ENDPOINT/media/upload" 2>/dev/null)
        if [ $? -eq 0 ]; then
            local cod_id=$(echo "$cod_response" | jq -r '.fileId // empty' 2>/dev/null)
            if [ -n "$cod_id" ] && [ "$cod_id" != "null" ]; then
                COD_FILE_ID="$cod_id"
                print_status "✅ COD screenshot uploaded with ID: $COD_FILE_ID"
            else
                print_warning "Using fallback file ID for COD screenshot: $COD_FILE_ID"
            fi
        else
            print_warning "Using fallback file ID for COD screenshot: $COD_FILE_ID"
        fi
    else
        print_warning "COD screenshot not found, using fallback ID: $COD_FILE_ID"
    fi

    # Try to upload Diablo screenshot
    print_status "Uploading Diablo screenshot to R2..."
    if [ -f "$ASSETS_DIR/diablo_screenshot.webp" ]; then
        local diablo_response=$(curl -s -X POST -F "file=@$ASSETS_DIR/diablo_screenshot.webp" "$R2_ENDPOINT/media/upload" 2>/dev/null)
        if [ $? -eq 0 ]; then
            local diablo_id=$(echo "$diablo_response" | jq -r '.fileId // empty' 2>/dev/null)
            if [ -n "$diablo_id" ] && [ "$diablo_id" != "null" ]; then
                DIABLO_FILE_ID="$diablo_id"
                print_status "✅ Diablo screenshot uploaded with ID: $DIABLO_FILE_ID"
            else
                print_warning "Using fallback file ID for Diablo screenshot: $DIABLO_FILE_ID"
            fi
        else
            print_warning "Using fallback file ID for Diablo screenshot: $DIABLO_FILE_ID"
        fi
    else
        print_warning "Diablo screenshot not found, using fallback ID: $DIABLO_FILE_ID"
    fi

    print_status "Media upload to R2 completed!"
}

# Seed Media table
seed_media() {
    print_header "Seeding Media table..."

    # Use the uploaded file IDs or fallback to default IDs
    local cod_id=${COD_FILE_ID:-"30000000-0000-0000-0000-********0001"}
    local diablo_id=${DIABLO_FILE_ID:-"30000000-0000-0000-0000-********0002"}

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "'$cod_id'"},
        "fileName": {"S": "cod_screenshot.jpg"},
        "fileType": {"S": "image/jpeg"},
        "fileSize": {"N": "245760"},
        "mediaType": {"S": "image"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "r2Key": {"S": "'$cod_id'"},
        "bucketName": {"S": "gameflex-media-dev"},
        "url": {"S": "'$R2_ENDPOINT'/media/'$cod_id'"},
        "status": {"S": "uploaded"},
        "created_at": {"S": "2024-12-28T14:00:00Z"},
        "updated_at": {"S": "2024-12-28T14:00:00Z"}
    }' "cod screenshot media"

    put_item "$MEDIA_TABLE" '{
        "id": {"S": "'$diablo_id'"},
        "fileName": {"S": "diablo_screenshot.webp"},
        "fileType": {"S": "image/webp"},
        "fileSize": {"N": "189440"},
        "mediaType": {"S": "image"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "r2Key": {"S": "'$diablo_id'"},
        "bucketName": {"S": "gameflex-media-dev"},
        "url": {"S": "'$R2_ENDPOINT'/media/'$diablo_id'"},
        "status": {"S": "uploaded"},
        "created_at": {"S": "2024-12-27T16:00:00Z"},
        "updated_at": {"S": "2024-12-27T16:00:00Z"}
    }' "diablo screenshot media"

    print_status "Media seeded successfully!"
}

# Seed Posts table
seed_posts() {
    print_header "Seeding Posts table..."

    # Use the uploaded file IDs or fallback to default IDs
    local cod_id=${COD_FILE_ID:-"30000000-0000-0000-0000-********0001"}
    local diablo_id=${DIABLO_FILE_ID:-"30000000-0000-0000-0000-********0002"}

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0001"},
        "author_id": {"S": "********-0000-0000-0000-********0003"},
        "userId": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥"},
        "media_id": {"S": "'$cod_id'"},
        "likes": {"N": "12"},
        "comments": {"N": "4"},
        "created_at": {"S": "2024-12-28T14:30:00Z"},
        "updated_at": {"S": "2024-12-28T14:30:00Z"}
    }' "john cod post"

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0002"},
        "author_id": {"S": "********-0000-0000-0000-********0004"},
        "userId": {"S": "********-0000-0000-0000-********0004"},
        "content": {"S": "Finally defeated this boss in Diablo! The loot was totally worth the grind 💀⚔️"},
        "media_id": {"S": "'$diablo_id'"},
        "likes": {"N": "18"},
        "comments": {"N": "6"},
        "created_at": {"S": "2024-12-27T16:45:00Z"},
        "updated_at": {"S": "2024-12-27T16:45:00Z"}
    }' "jane diablo post"

    put_item "$POSTS_TABLE" '{
        "id": {"S": "20000000-0000-0000-0000-********0003"},
        "author_id": {"S": "********-0000-0000-0000-********0005"},
        "userId": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "Anyone else excited for the new gaming releases this month? What are you most looking forward to? 🎮"},
        "likes": {"N": "5"},
        "comments": {"N": "2"},
        "created_at": {"S": "2024-12-26T10:15:00Z"},
        "updated_at": {"S": "2024-12-26T10:15:00Z"}
    }' "mike text post"

    print_status "Posts seeded successfully!"
}

# Seed Comments table
seed_comments() {
    print_header "Seeding Comments table..."

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0001"},
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0004"},
        "content": {"S": "Nice clutch! What loadout were you using?"},
        "like_count": {"N": "3"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T15:15:00Z"},
        "updated_at": {"S": "2024-12-28T15:15:00Z"}
    }' "comment 1"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0002"},
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "content": {"S": "That was insane! 🔥"},
        "like_count": {"N": "1"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-28T16:20:00Z"},
        "updated_at": {"S": "2024-12-28T16:20:00Z"}
    }' "comment 2"

    put_item "$COMMENTS_TABLE" '{
        "id": {"S": "40000000-0000-0000-0000-********0003"},
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "content": {"S": "Congrats! What difficulty level?"},
        "like_count": {"N": "2"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-12-27T17:30:00Z"},
        "updated_at": {"S": "2024-12-27T17:30:00Z"}
    }' "comment 3"

    print_status "Comments seeded successfully!"
}

# Seed Likes table
seed_likes() {
    print_header "Seeding Likes table..."

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0004"},
        "created_at": {"S": "2024-12-28T14:35:00Z"}
    }' "like 1"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0001"},
        "user_id": {"S": "********-0000-0000-0000-********0005"},
        "created_at": {"S": "2024-12-28T14:40:00Z"}
    }' "like 2"

    put_item "$LIKES_TABLE" '{
        "post_id": {"S": "20000000-0000-0000-0000-********0002"},
        "user_id": {"S": "********-0000-0000-0000-********0003"},
        "created_at": {"S": "2024-12-27T16:50:00Z"}
    }' "like 3"

    print_status "Likes seeded successfully!"
}

# Seed Follows table
seed_follows() {
    print_header "Seeding Follows table..."

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "following_id": {"S": "550e8400-e29b-41d4-a716-************"},
        "created_at": {"S": "2024-01-01T00:00:00Z"}
    }' "dev follows admin"

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "********-0000-0000-0000-********0003"},
        "following_id": {"S": "********-0000-0000-0000-********0004"},
        "created_at": {"S": "2024-01-02T00:00:00Z"}
    }' "john follows jane"

    put_item "$FOLLOWS_TABLE" '{
        "follower_id": {"S": "********-0000-0000-0000-********0004"},
        "following_id": {"S": "********-0000-0000-0000-********0005"},
        "created_at": {"S": "2024-01-03T00:00:00Z"}
    }' "jane follows mike"

    print_status "Follows seeded successfully!"
}

# Print summary
print_summary() {
    print_status "Summary:"
    print_status "  📊 Seeded 5 users with profiles"
    print_status "  📝 Seeded 3 posts (2 with media, 1 text-only)"
    print_status "  💬 Seeded 3 comments"
    print_status "  ❤️  Seeded 3 likes"
    print_status "  👥 Seeded 3 follow relationships"
    print_status "  📸 Seeded 2 media items"
    print_status ""
    print_status "Your GameFlex LocalStack backend is now ready for testing!"
    print_status "LocalStack endpoint: $LOCALSTACK_ENDPOINT"
    print_status "DynamoDB Admin: http://localhost:8001"
    print_status ""
    print_status "Test the health endpoint:"
    print_status "  curl $LOCALSTACK_ENDPOINT/health"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    print_error "Ubuntu/Debian: sudo apt-get install jq"
    print_error "macOS: brew install jq"
    exit 1
fi

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
