#!/bin/bash

# GameFlex R2 Development Environment Test Script
# This script tests the Cloudflare R2 development setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[R2-TEST]${NC} $1"
}

# Test configuration
R2_BASE_URL="http://localhost:8787"
TEST_FILE="test-image.jpg"

print_header "GameFlex R2 Development Simulator Test"
echo

# Test 1: Health check
print_status "Testing R2 health endpoint..."
if curl -s "${R2_BASE_URL}/health" > /dev/null 2>&1; then
    HEALTH_RESPONSE=$(curl -s "${R2_BASE_URL}/health")
    echo "✅ R2 health check passed"
    echo "Response: $HEALTH_RESPONSE" | jq . 2>/dev/null || echo "Response: $HEALTH_RESPONSE"
else
    print_error "❌ R2 health check failed"
    print_warning "Make sure the R2 development simulator is running: ./start.sh"
    exit 1
fi

echo

# Test 2: List buckets
print_status "Testing bucket listing..."
if curl -s "${R2_BASE_URL}/buckets" > /dev/null 2>&1; then
    BUCKETS_RESPONSE=$(curl -s "${R2_BASE_URL}/buckets")
    echo "✅ Bucket listing successful"
    echo "Response: $BUCKETS_RESPONSE" | jq . 2>/dev/null || echo "Response: $BUCKETS_RESPONSE"
else
    print_error "❌ Bucket listing failed"
fi

echo

# Test 3: File upload (create a test file)
print_status "Creating test file for upload..."
echo "This is a test file for GameFlex R2 development" > "/tmp/${TEST_FILE}"

print_status "Testing file upload..."
if command -v curl > /dev/null 2>&1; then
    UPLOAD_RESPONSE=$(curl -s -X POST \
        -F "file=@/tmp/${TEST_FILE}" \
        "${R2_BASE_URL}/media/upload")
    
    if echo "$UPLOAD_RESPONSE" | grep -q "success"; then
        echo "✅ File upload successful"
        echo "Response: $UPLOAD_RESPONSE" | jq . 2>/dev/null || echo "Response: $UPLOAD_RESPONSE"
        
        # Extract file ID for download test
        FILE_ID=$(echo "$UPLOAD_RESPONSE" | jq -r '.fileId' 2>/dev/null || echo "")
        
        if [ -n "$FILE_ID" ] && [ "$FILE_ID" != "null" ]; then
            echo
            print_status "Testing file download..."
            if curl -s "${R2_BASE_URL}/media/${FILE_ID}" > "/tmp/downloaded_${TEST_FILE}"; then
                echo "✅ File download successful"
                
                # Compare files
                if cmp -s "/tmp/${TEST_FILE}" "/tmp/downloaded_${TEST_FILE}"; then
                    echo "✅ File integrity verified"
                else
                    print_warning "⚠️  File integrity check failed"
                fi
                
                echo
                print_status "Testing file deletion..."
                DELETE_RESPONSE=$(curl -s -X DELETE "${R2_BASE_URL}/media/${FILE_ID}")
                if echo "$DELETE_RESPONSE" | grep -q "success"; then
                    echo "✅ File deletion successful"
                else
                    print_warning "⚠️  File deletion may have failed"
                    echo "Response: $DELETE_RESPONSE"
                fi
            else
                print_error "❌ File download failed"
            fi
        else
            print_warning "⚠️  Could not extract file ID from upload response"
        fi
    else
        print_error "❌ File upload failed"
        echo "Response: $UPLOAD_RESPONSE"
    fi
else
    print_error "❌ curl command not found"
fi

# Cleanup
rm -f "/tmp/${TEST_FILE}" "/tmp/downloaded_${TEST_FILE}"

echo
print_header "R2 Development Simulator Test Complete"
echo

print_status "Summary:"
echo "  🌐 R2 Dev Simulator: ${R2_BASE_URL}"
echo "  📋 Available endpoints:"
echo "    - Health: ${R2_BASE_URL}/health"
echo "    - Upload: ${R2_BASE_URL}/media/upload"
echo "    - Download: ${R2_BASE_URL}/media/{id}"
echo "    - Buckets: ${R2_BASE_URL}/buckets"
echo
print_status "To interact with R2 manually:"
echo "  curl ${R2_BASE_URL}/health"
echo "  curl -X POST -F 'file=@your-file.jpg' ${R2_BASE_URL}/media/upload"
echo "  curl ${R2_BASE_URL}/buckets"
