version: '3.8'

services:
  localstack:
    container_name: gameflex-localstack
    image: localstack/localstack:3.0
    ports:
      - "4566:4566"            # LocalStack Gateway
      - "4510-4559:4510-4559"  # External services port range
    environment:
      # LocalStack configuration
      - DEBUG=1
      - SERVICES=lambda,dynamodb,apigateway,logs,iam,sts
      - DOCKER_HOST=unix:///var/run/docker.sock
      - LAMBDA_EXECUTOR=docker-reuse
      - LAMBDA_REMOVE_CONTAINERS=true
      - LAMBDA_DOCKER_NETWORK=gameflex_default
      - PERSISTENCE=1
      - DATA_DIR=/tmp/localstack/data
      
      # AWS configuration
      - AWS_DEFAULT_REGION=us-west-2
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      
      # GameFlex specific configuration
      - ENVIRONMENT=development
      - PROJECT_NAME=gameflex
      
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./tmp/localstack}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./src:/opt/code/localstack/src"
      - "./scripts:/opt/code/localstack/scripts"
    networks:
      - gameflex

  # Optional: DynamoDB Admin UI for easier database management
  dynamodb-admin:
    container_name: gameflex-dynamodb-admin
    image: aaronshaf/dynamodb-admin:latest
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://localstack:4566
      - AWS_REGION=us-west-2
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    depends_on:
      - localstack
    networks:
      - gameflex

networks:
  gameflex:
    driver: bridge

volumes:
  localstack_data:
    driver: local
