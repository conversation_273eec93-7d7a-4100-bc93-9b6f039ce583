services:
  localstack:
    container_name: gameflex-localstack
    image: localstack/localstack:3.0
    ports:
      - "4566:4566"            # LocalStack Gateway
      - "4510-4559:4510-4559"  # External services port range
    environment:
      # LocalStack configuration
      - DEBUG=1
      - SERVICES=lambda,dynamodb,apigateway,logs,iam,sts
      - DOCKER_HOST=unix:///var/run/docker.sock
      - LAMBDA_EXECUTOR=local
      - LAMBDA_REMOVE_CONTAINERS=true
      - PERSISTENCE=1
      - DATA_DIR=/tmp/localstack/data
      - LOCALSTACK_AUTH_TOKEN=ls-NIdUsIdA-lIza-5087-Fuji-jorOpuPAb667

      # AWS configuration
      - AWS_DEFAULT_REGION=us-west-2
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test

      # GameFlex specific configuration
      - ENVIRONMENT=development
      - PROJECT_NAME=gameflex
      
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./tmp/localstack}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./src:/opt/code/localstack/src"
      - "./scripts:/opt/code/localstack/scripts"
    networks:
      - gameflex

  # Optional: DynamoDB Admin UI for easier database management
  dynamodb-admin:
    container_name: gameflex-dynamodb-admin
    image: aaronshaf/dynamodb-admin:latest
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://localstack:4566
      - AWS_REGION=us-west-2
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    depends_on:
      - localstack
    networks:
      - gameflex

  # Cloudflare R2 Development Simulator
  cloudflare-r2:
    container_name: gameflex-cloudflare-r2
    image: node:20-alpine
    working_dir: /app
    ports:
      - "8787:8787"  # R2 simulator server
    environment:
      - NODE_ENV=development
      - PORT=8787
      - STORAGE_PATH=/app/storage
      - LOCALSTACK_ENDPOINT=http://host.docker.internal:4566
    volumes:
      - "./cloudflare:/app"
      - "./tmp/cloudflare:/app/storage"
      - "cloudflare_node_modules:/app/node_modules"
    command: >
      sh -c "
      echo 'Initializing R2 development simulator...' &&
      if [ ! -f package.json ]; then
        echo 'Creating package.json...' &&
        npm init -y
      fi &&
      echo 'Installing dependencies...' &&
      npm install express@latest multer@latest cors@latest uuid@latest &&
      echo 'Dependencies installed successfully' &&
      echo 'Starting Cloudflare R2 development simulator...' &&
      node server.js
      "
    networks:
      - gameflex
    restart: unless-stopped

networks:
  gameflex:
    driver: bridge

volumes:
  localstack_data:
    driver: local
  cloudflare_node_modules:
    driver: local
