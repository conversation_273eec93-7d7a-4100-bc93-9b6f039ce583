# GameFlex LocalStack Environment Configuration
# This file contains environment variables for LocalStack development

# LocalStack Configuration
LOCALSTACK_HOST=localhost
LOCALSTACK_ENDPOINT=http://localhost:4566
AWS_ENDPOINT_URL=http://localhost:4566
LOCALSTACK_AUTH_TOKEN=ls-NIdUsIdA-lIza-5087-Fuji-jorOpuPAb667

# AWS Configuration for LocalStack
AWS_DEFAULT_REGION=us-west-2
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

# Project Configuration
ENVIRONMENT=development
PROJECT_NAME=gameflex
DEBUG=1

# API Configuration
API_PORT=3000
API_GATEWAY_URL=http://localhost:4566/restapis

# DynamoDB Configuration
DYNAMODB_ENDPOINT=http://localhost:4566
DYNAMODB_ADMIN_URL=http://localhost:8001

# Table Names (LocalStack format)
USERS_TABLE=gameflex-development-Users
POSTS_TABLE=gameflex-development-Posts
MEDIA_TABLE=gameflex-development-Media
USER_PROFILES_TABLE=gameflex-development-UserProfiles
COMMENTS_TABLE=gameflex-development-Comments
LIKES_TABLE=gameflex-development-Likes
FOLLOWS_TABLE=gameflex-development-Follows

# Lambda Configuration
LAMBDA_ENDPOINT=http://localhost:4566

# IAM Configuration (LocalStack doesn't require real values)
USER_POOL_ID=us-west-2_localstack
USER_POOL_CLIENT_ID=localstack_client_id

# S3 Configuration (for future use)
S3_ENDPOINT=http://localhost:4566
MEDIA_BUCKET=gameflex-media-development
AVATARS_BUCKET=gameflex-avatars-development
TEMP_BUCKET=gameflex-temp-development

# LocalStack specific settings
LOCALSTACK_VOLUME_DIR=./tmp/localstack
LAMBDA_EXECUTOR=docker-reuse
LAMBDA_REMOVE_CONTAINERS=true
PERSISTENCE=1
